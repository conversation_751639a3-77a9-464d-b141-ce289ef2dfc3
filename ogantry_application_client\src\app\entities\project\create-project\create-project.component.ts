import { After<PERSON><PERSON>wInit, ChangeDetectorRef, Component, ElementRef, On<PERSON>estroy, OnInit, ViewChild } from '@angular/core';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams, CustomerContact } from '@shared/models';
import { MatSidenav } from '@angular/material/sidenav';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { Client, Contact } from '@entities/client/client.model';
import { DailyExpenseType, MonthlyExpenseType, Project, BillingTypes, ProjectProjections } from '../project.model';
import { PositionSetupComponent } from '@entities/project/position-setup/position-setup.component';
import { ProjectCostComponent } from '../project-cost/project-cost.component';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { ProjectService } from '@entities/project/project.service';
import { ProjectSetupComponent } from '@entities/project/project-setup/project-setup.component';
import { KtDialogService } from '@shared/services';
import { LayoutConfigService } from '@shared/services/layout-config.service';
import { ClientService } from '@entities/client/client.service';
import { interval, Subscription, of, throwError, Observable, Subject } from 'rxjs';
import { map, retryWhen, delay, take, switchMap, catchError } from 'rxjs/operators';
import { AdministrationService } from '@entities/administration/administration.service';
import { ComponentsType } from '@shared/models/component-type-enum';
import { PurchaseOrderService } from '@shared/services/purchase-order.service';
import { ProjectAPIState, ProjectButtonState, ProjectUserState, PurchaseOrderList } from '@shared/models/custom/purchase-order.model';
import { CanComponentDeactivate } from '@entities/manage-people/guards/can-deactivate.guard';
import { AppConstants } from '@shared/constants/app.constant';
import { FinancialReviewComponent } from '../financial-review/financial-review.component';

interface CustomerContactResponse {
  project_contact: CustomerContact;
}

@Component({
  selector: 'app-create-project',
  templateUrl: './create-project.component.html',
  styleUrls: ['./create-project.component.scss']
})
export class CreateProjectComponent extends SflBaseComponent implements OnInit, AfterViewInit, OnDestroy, CanComponentDeactivate {
  currentProjectState: ProjectUserState = ProjectUserState.Paused;
  projectAPIState = ProjectAPIState;
  projectStateLabel: ProjectButtonState = ProjectButtonState.Resume;
  extendFieldsObj: any = {};
  componentType = ComponentsType;
  isExtendFiled = false;
  dateObj: any;
  openFilter = false;
  globalDetailId: any;
  cardTitle = 'Project Management';
  cardSubTitle = null;
  buttons: ButtonParams[] = [];
  @ViewChild('wizard', { static: true }) el: ElementRef;
  @ViewChild('postionSetup') postionList: PositionSetupComponent;
  @ViewChild('financialReview') financialReview: FinancialReviewComponent;
  clientId: number | null;
  listOFPurchaseOrder: PurchaseOrderList | Array<any> = [];
  purchaseOrderSidebarVisible = false;
  submitted = false;
  isAddContactVisible = false;
  selectedTemplate: MatSidenav;
  sidebarParams: SidebarParams<CustomerContactResponse>;
  project: Project;
  selectedContact: Contact;
  isLastStep = false;
  projectId: number;
  isEdit = false;
  overAllProjectData: Project;
  wizardStep = 1;
  @ViewChild(PositionSetupComponent, { static: false }) positionSetup;
  @ViewChild(ProjectSetupComponent, { static: false }) projectSetup;
  @ViewChild(ProjectCostComponent, { static: false }) projectCost;
  validateProject = true;
  wizard = new KTWizard();
  oldProjectId: string;
  dailyExpenseTypes: DailyExpenseType[] = [];
  monthlyExpenseTypes: MonthlyExpenseType[] = [];
  isAddNewClient = false;
  customers = [];
  hasAnyPosition = false;
  isClickable = false;
  subscription: Subscription;
  validationStatus: string;
  time = interval(15000);
  tags: string[];
  projectionData: ProjectProjections;
  projectCostDataLoading = true;
  recalculating = false;
  extendFields: any;
  isCopyProject = false;
  customerId: number;
  showLeavePageDialog = false;
  rememberChoice = false;
  pendingNavigation: string;
  navigationSubject = new Subject<boolean>();
  isResumeValidationInProgress = false;
  pendingStepNavigation = null;
  startDate: Date;
  endDate: Date;
  constructor(
    private readonly layoutConfigService: LayoutConfigService,
    private readonly ktDialogService: KtDialogService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly projectService: ProjectService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly cdf: ChangeDetectorRef,
    private readonly adminService: AdministrationService,
    private readonly clientService: ClientService,
    private readonly purchaseOrderService: PurchaseOrderService
  ) {
    super();
  }

  async ngOnInit() {
    this.getParams();
    this.getGlobalDetailsCategory();
    this.getClientList();
    this.getDailyExpenseType();
    this.getMonthlyExpenseType();
    this.subscription = this.time.subscribe((val) => {
      this.checkValidationStatus();
    });
    this.route.queryParams.subscribe((params) => {
      this.isCopyProject = params['copy'] === 'true' ? true : false;
    });

    const savedChoice = sessionStorage.getItem(AppConstants.SESSION_STORAGE.LEAVE_PROJECT_PAGE_CHOICE);
    if (savedChoice) {
      this.rememberChoice = true;
    }
  }

  checkValidationStatus() {
    if (this.projectId) {
      this.subscriptionManager.add(
        this.projectService.getValidationStatus(this.projectId).subscribe((res) => {
          if (this.validationStatus !== res.data.project.validated) {
            this.validationStatus = res.data.project.validated;
            if (this.validationStatus === ProjectAPIState.Validated) {
              this.getProjections();
              // this.financialReview?.ngOnInit();
              this.currentProjectState = ProjectUserState.Validated;
            } else if (this.validationStatus === ProjectAPIState.NotValidate || this.validationStatus === ProjectAPIState.INPROGRESS) {
              this.recalculateData();
              this.currentProjectState = ProjectUserState.Recalculating;
            } else if (this.validationStatus === ProjectAPIState.Paused) {
              this.currentProjectState = ProjectUserState.Paused;
            }
            this.projectStateLabel = ProjectButtonState.ReCalculate;
            this.cdf.detectChanges();
          }
        })
      );
    }
  }

  getClientList() {
    this.subscriptionManager.add(
      this.clientService.getClientData({ is_active: true, order_by: 'asc:name' }).subscribe((res) => {
        res.body.data.customers.map((cust) => {
          this.customers.push({
            name: cust.customer.name,
            id: cust.customer.id
          });
        });
        this.cdf.detectChanges();
      })
    );
  }

  getParams() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.projectId = params.projectId;
      this.oldProjectId = params.projectId;
      this.getUpdateProjectData();
    });
  }

  getProjections() {
    let flag = 0;
    const params = {
      projection_detail_level: 'position_monthly',
      include_work_exceptions: true,
      include_utilizations: false
    };
    this.subscriptionManager.add(
      this.projectService
        .getProjections(this.projectId, params)
        .pipe(
          map((res) => {
            if (res.status === 202) {
              flag++;
              throw res;
            }
            return res;
          }),
          retryWhen((errors) =>
            errors.pipe(
              switchMap((val) => {
                // retry only when res.status code is 202 and we won't keep on calling the service for other status code
                if (flag >= 15 && (val.status === 202 || val?.error?.status === 202)) {
                  return of(val).pipe(delay(15000), take(2));
                } else if (flag < 15 && (val.status === 202 || val?.error?.status === 202)) {
                  return of(val).pipe(delay(1000), take(15));
                } else {
                  return throwError(errors);
                }
              })
            )
          ),
          catchError((err) => throwError(err))
        )
        .subscribe((res) => {
          if (res.body.message !== 'Processing your request... check back soon.') {
            this.projectionData = res.body.data;
            this.projectCostDataLoading = false;
            this.recalculating = false;
            this.projectService.setRecalculatingTheRevenueSubject(false);
            this.cdf.detectChanges();
          }
        })
    );
  }

  recalculateData() {
    this.recalculating = true;
    this.projectService.setRecalculatingTheRevenueSubject(true);
    this.projectCostDataLoading = true;
    this.projectionData = undefined;
    this.cdf.detectChanges();
  }

  getDailyExpenseType() {
    this.subscriptionManager.add(
      this.projectService.getDailyExpenseType().subscribe((res) => {
        res.data.daily_expense_types.forEach((type) => {
          this.dailyExpenseTypes.push({
            label: type.daily_expense_type.name,
            value: type.daily_expense_type.id
          });
        });
      })
    );
  }

  getMonthlyExpenseType() {
    this.subscriptionManager.add(
      this.projectService.getMonthlyExpenseType().subscribe((res) => {
        res.data.monthly_expense_types.forEach((type) => {
          this.monthlyExpenseTypes.push({
            label: type.monthly_expense_type.name,
            value: type.monthly_expense_type.id
          });
        });
      })
    );
  }

  goToNextStep(step) {
    if (this.isEdit) {
      if (Number(this.wizardStep) === 2 && Number(step) !== 2) {
        if (this.validationStatus == ProjectAPIState.Paused) {
          const savedChoice = sessionStorage.getItem(AppConstants.SESSION_STORAGE.LEAVE_PROJECT_PAGE_CHOICE);

          if (savedChoice) {
            if (savedChoice === AppConstants.SESSION_STORAGE_VALUES.FALSE) {
              this.resumeValidation();
              this.proceedToStep(step);
            } else {
              this.proceedToStep(step);
            }
          } else {
            this.showLeavePageDialog = true;
            this.pendingStepNavigation = step;
            return;
          }
        } else {
          this.proceedToStep(step);
        }
      } else {
        this.proceedToStep(step);
      }
    }
  }

  private proceedToStep(step: string | Number): void {
    this.wizard.goTo(step);
    this.wizardStep = Number(step);
    if (step === '2') {
      this.positionSetup.getPositionList();
      this.positionSetup.getEmployeeList();
      if (this.overAllProjectData?.project?.billing_type === BillingTypes.TIME_MATERIALS) {
        this.positionSetup.setBillRate(true);
      }
      this.isLastStep = false;
    }
    if (step === '3') {
      this.positionSetup.nextStep();
    }
  }

  ngAfterViewInit(): void {
    if (this.el) {
      // Initialize form wizard
      this.wizard = new KTWizard(this.el.nativeElement, {
        startStep: this.isEdit && !this.isCopyProject ? 2 : 1,
        clickableSteps: false
      });
      this.wizardStep = this.isEdit && !this.isCopyProject ? 2 : 1;

      if (this.wizard?.currentStep === 2) {
        this.positionSetup?.getEmployeeList();
        if (this.overAllProjectData?.project?.billing_type === BillingTypes.TIME_MATERIALS) {
          this.positionSetup.setBillRate(true);
        }
        this.isLastStep = false;
      }

      // Validation before going to next page
      this.wizard.on('beforeNext', (wizardObj) => {
        // https://angular.io/guide/forms
        // https://angular.io/guide/form-validation
        // validate the form and use below function to stop the wizard's step
        wizardObj.stop();
      });

      // Change event
      this.wizard.on('change', (wizard) => {
        this.wizardStep = wizard.currentStep;
        if (wizard.currentStep === 1) {
          this.isLastStep = false;
        }
        if (wizard.currentStep === 2) {
          this.positionSetup.getPositionList();
          this.positionSetup.getEmployeeList();
          if (this.overAllProjectData?.project?.billing_type === BillingTypes.TIME_MATERIALS) {
            this.positionSetup.setBillRate(true);
          }
          this.isLastStep = false;
        }
        if (wizard.currentStep === 3) {
          this.isLastStep = true;
        }
        setTimeout(() => {
          KTUtil.scrollTop();
          this.layoutConfigService.updateHeight$.next(true);
          this.cdf.detectChanges();
        }, 500);
      });
    }
    this.callProjectionApi(true);
  }

  callProjectionApi(flag = false) {
    if (flag) {
      // this.projectCost.getProjections(this.overAllProjectData);
    }
  }

  onSubmit() {
    this.submitted = true;
  }

  callProjectProjectionApi(flag = false) {
    if (flag) {
      this.projectSetup.getProjectData();
    }
  }

  setProjectData(data) {
    this.overAllProjectData = data;
    this.project = data;
    if (this.overAllProjectData?.project?.billing_type === BillingTypes.TIME_MATERIALS) {
      this.positionSetup.setBillRate(true);
    }
    // this.projectCost.getProjections(data);
    this.cdf.detectChanges();
  }

  openSidebar(sidebarParams: SidebarParams<CustomerContactResponse>, eventRes): void {
    this.project = eventRes.project;
    this.project.isEdit = eventRes.isEdit;

    this.selectedContact = eventRes.selectedContactPerson;
    this.isAddContactVisible = eventRes.isAddContactVisible;
    this.sidebarParams = sidebarParams;
    // this.sidebarParams.template.toggle();
    this.openFilter = true;
  }

  sidebarClosed(isClosed: boolean) {
    if (isClosed) {
      this.isAddContactVisible = false;
      this.isAddNewClient = false;
      this.isExtendFiled = false;
      // this.sidebarParams.template.toggle();
      this.openFilter = false;
    }
  }

  openClientSidebar(sidebarParams: SidebarParams<CustomerContactResponse>, eventRes): void {
    this.isAddNewClient = true;
    this.sidebarParams = sidebarParams;
    // this.sidebarParams.template.toggle();
    this.openFilter = true;
  }

  // update the contact list with the updated contact
  updateClientList(sidebarParams: SidebarParams<CustomerContactResponse>) {
    this.projectService.getProject(this.projectId).subscribe((res) => {
      this.project = res.data;
    });
    this.projectSetup.detectChanges();
  }

  setProjectId(value) {
    this.projectId = value;
    this.isEdit = true;
    this.wizard.options.clickableSteps = true;
    this.cdf.detectChanges();
    if (this.isEdit) {
      this.router.navigateByUrl(`${this.appRoutes.CREATE_PROJECT}/${value}`);
    }
  }

  getProjectionData(value) {
    setTimeout(() => {
      this.callProjectionApi(true);
    }, 2000);
  }

  goToThirdStep(): void {
    this.goToNextStep(3);
  }

  goToSecondStep() {
    this.wizard.goPrev();
    this.wizardStep = 2;
    this.validateProject = false;
    this.isLastStep = false;
  }

  gotoPositionSetupTab(): void {
    this.wizard.goNext();
    this.validateProject = false;
    this.isLastStep = false;
  }

  ngOnDestroy() {
    this.ktDialogService.hide();
    this.subscription.unsubscribe();
  }

  canDeactivate(): Observable<boolean> {
    if (this.validationStatus == ProjectAPIState.Paused) {
      const savedChoice = sessionStorage.getItem(AppConstants.SESSION_STORAGE.LEAVE_PROJECT_PAGE_CHOICE);

      if (savedChoice) {
        if (savedChoice === AppConstants.SESSION_STORAGE_VALUES.FALSE) {
          this.resumeValidation();
        }
        return of(true);
      }
      if (this.wizardStep === 2) {
        this.showLeavePageDialog = true;
        return this.navigationSubject.asObservable();
      }
    }

    return of(true);
  }

  stayOnPage(): void {
    this.showLeavePageDialog = false;

    if (this.rememberChoice) {
      sessionStorage.setItem(AppConstants.SESSION_STORAGE.LEAVE_PROJECT_PAGE_CHOICE, AppConstants.SESSION_STORAGE_VALUES.FALSE);
    } else {
      sessionStorage.removeItem(AppConstants.SESSION_STORAGE.LEAVE_PROJECT_PAGE_CHOICE);
    }

    this.navigationSubject.next(false);
  }

  leavePage(): void {
    this.showLeavePageDialog = false;

    if (this.rememberChoice) {
      sessionStorage.setItem(AppConstants.SESSION_STORAGE.LEAVE_PROJECT_PAGE_CHOICE, AppConstants.SESSION_STORAGE_VALUES.TRUE);
    } else {
      sessionStorage.removeItem(AppConstants.SESSION_STORAGE.LEAVE_PROJECT_PAGE_CHOICE);
    }

    // Handle pending navigation if exists
    if (this.pendingStepNavigation) {
      this.proceedToStep(this.pendingStepNavigation);
      this.pendingStepNavigation = null;
    } else {
      this.navigationSubject.next(true);
    }
  }

  resumeAndStay(): void {
    this.showLeavePageDialog = false;

    if (this.rememberChoice) {
      sessionStorage.setItem(AppConstants.SESSION_STORAGE.LEAVE_PROJECT_PAGE_CHOICE, AppConstants.SESSION_STORAGE_VALUES.FALSE);
    } else {
      sessionStorage.removeItem(AppConstants.SESSION_STORAGE.LEAVE_PROJECT_PAGE_CHOICE);
    }

    this.resumeValidation();

    // Handle pending navigation if exists
    if (this.pendingStepNavigation) {
      this.proceedToStep(this.pendingStepNavigation);
      this.pendingStepNavigation = null;
    } else {
      this.navigationSubject.next(true);
    }
  }

  goToNextTab() {
    this.wizard.goNext();
    this.wizardStep = 2;
    this.positionSetup.getEmployeeList();
    if (this.overAllProjectData?.project?.billing_type === BillingTypes.TIME_MATERIALS) {
      this.positionSetup.setBillRate(true);
    }
  }

  updateCustomerList(client: Client) {
    if (client?.customer?.useNewClient) {
      this.projectSetup.setCustomerId(client);
      this.getClientList();
    } else {
      this.projectSetup.setClientFlag();
    }
    this.cdf.detectChanges();
  }
  hasPositions(position) {
    if (position?.length) {
      this.hasAnyPosition = true;
    } else {
      this.hasAnyPosition = false;
    }
  }
  getGlobalDetailsCategory() {
    this.subscriptionManager.add(
      this.adminService.getExtendedFields('ManageExtendedFiled').subscribe(
        (res) => {
          this.projectCostDataLoading = false;
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'ManageExtendedFiled') {
              this.extendFields = res.data.global_details[0].global_detail.extended_fields.extendArray || [];
              this.globalDetailId = res.data.global_details[0].global_detail.id;
              // this.processDataWorkOnGlobalDetails(this.extendFields);
              // this.adminService.setExtendedFiled(res.data.global_details[0].global_detail);
              //     this.tagCategory = globalDetail[0].global_detail.extended_fields.tagCategory;
            }
          }
        },
        () => (this.projectCostDataLoading = false)
      )
    );
  }

  openSidebarExtendFiled(event) {
    this.isExtendFiled = event;
    this.openFilter = true;
  }

  openPurchaseOrderSidebar(): void {
    this.openFilter = true;
    this.purchaseOrderSidebarVisible = true;
  }

  closePurchaseOrderSidebar(): void {
    this.openFilter = false;
    this.purchaseOrderSidebarVisible = false;
  }

  listPurchaseOrder(customer_id: number): void {
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.purchaseOrderService.purchaseOrderListBYCustomer(customer_id).subscribe({
        next: (res) => {
          this.listOFPurchaseOrder = res?.data.purchase_orders;
          this.loading$.next(false);
        },
        error: () => {
          this.loading$.next(false);
        }
      })
    );
  }

  getUpdateProjectData(): void {
    if (this.projectId) {
      this.isEdit = true;
      this.subscriptionManager.add(
        this.projectService.getProject(this.projectId).subscribe((res) => {
          this.project = res.data;
          this.clientId = res?.data?.project?.customer.id ? res.data.project.customer.id : null;
          this.overAllProjectData = res.data;
          this.extendFieldsObj = res?.data?.project?.extended_fields;
          this.dateObj = {
            start_date: this.overAllProjectData?.project?.start_date,
            end_date: this.overAllProjectData?.project?.end_date
          };
          this.startDate = new Date(this.overAllProjectData?.project?.start_date);
          this.endDate = new Date(this.overAllProjectData?.project?.end_date);
          this.tags = res.data?.project?.tags;
          this.checkValidationStatus();
          // Todo future use case
          // this.callProjectionApi(true);
          if (this.overAllProjectData?.project?.billing_type === BillingTypes.TIME_MATERIALS) {
            this.positionSetup.setBillRate(true);
          }
          this.cdf.detectChanges();
          this.projectSetup.setProjectForm();
        })
      );
    }
  }

  resumeValidation(): void {
    if (!this.isResumeValidationInProgress) {
      this.resumeProjectValidation(this.projectId);
    }
  }

  resumeProjectValidation(projectId: number): void {
    this.isResumeValidationInProgress = true;
    this.subscriptionManager.add(
      this.projectService.resumeProjectValidation(projectId).subscribe({
        complete: () => {
          this.recalculateData();
          this.checkValidationStatus();
          this.isResumeValidationInProgress = false;
        }
      })
    );
  }
}
