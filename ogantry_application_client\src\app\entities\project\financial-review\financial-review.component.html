<div class="card-body p-4" id="financialReview">
  <div class="row py-4 backround-color-row-1">
    <div class="col-6">
      <H3 class="font-weight-bold pl-4">Financial Forecast</H3>
    </div>
    <div class="col-6">
      <div class="d-flex justify-content-end flex-nowrap align-items-center">
        <div class="d-flex align-items-center">
          <label class="font-weight-bold px-2 align-self-end">From :</label>
          <div>
            <p-calendar
              class="edit-cal"
              appendTo="body"
              view="month"
              [(ngModel)]="startDate"
              [maxDate]="maxDate"
              [minDate]="minDate"
              dateFormat="M/y"
              [yearNavigator]="true"
              [yearRange]="yearRange"
              [readonlyInput]="true"
              inputId="monthpicker"
              placeholder="Month/Year"
              (ngModelChange)="onDateChange()"
            >
            </p-calendar>
            <small class="text-danger d-block position-absolute" *ngIf="!startDate">Start Month is Required</small>
            <small class="text-danger d-block position-absolute" *ngIf="startDateError">Start Month is smaller than End Month</small>
          </div>
        </div>
        <div class="mx-2 d-flex align-items-center">
          <label class="font-weight-bold px-2 align-self-end">To :</label>
          <div>
            <p-calendar
              class="edit-cal"
              appendTo="body"
              view="month"
              [(ngModel)]="endDate"
              [maxDate]="maxDate"
              [minDate]="startDate || minDate"
              dateFormat="M/y"
              [yearNavigator]="true"
              [yearRange]="yearRange"
              [readonlyInput]="true"
              inputId="monthpicker"
              placeholder="Month/Year"
              (ngModelChange)="onDateChange()"
            >
            </p-calendar>
            <small class="text-danger d-block position-absolute" *ngIf="!endDate">End Month is Required</small>
            <small class="text-danger d-block position-absolute" *ngIf="startDateError">End Month is greater than Start Month</small>
          </div>
        </div>
        <div class="mx-2">
          <a title="Reset Filter" (click)="resetSelectedMonthFilter()" class="btn btn-icon btn-icon-light btn-light btn-sm icon-background">
            <span title="Reset Filter" [inlineSVG]="imageConst.purchaseOrderIcon" cacheSVG="true" class="svg-icon svg-icon-md"> </span>
          </a>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="!resizeFlag; else resize_table">
    <p-treeTable
      [value]="financialReview"
      *isFetchingData="loading$"
      [columns]="tableHeaders"
      frozenWidth="450px"
      [frozenColumns]="frozenCols"
      [scrollable]="true"
      (onNodeExpand)="makeRowsSameHeight()"
      (onNodeCollapse)="makeRowsSameHeight()"
    >
      <ng-template pTemplate="colgroup" let-columns>
        <colgroup>
          <col *ngFor="let col of columns" style="width: 110px" />
          <col style="width: 110px" />
        </colgroup>
      </ng-template>
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th *ngFor="let col of columns">
            {{ col.monthLabel }}
          </th>
          <th>Total</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowNode let-rowData="rowData">
        <tr>
          <td *ngFor="let monthlyData of getMonthlyValues(rowData) | keyvalue : preserveOriginalOrder; let index">
            <span
              [ngClass]="
                getLockUnlockSign(monthlyData, index) === 'unlock' || project?.project?.billing_type === billingTypes.MONTHLY_RETAINER
                  ? project?.project?.billing_type === billingTypes.MONTHLY_RETAINER
                    ? 'retainer-plug-wrapper'
                    : 'underline'
                  : ''
              "
              *ngIf="
                rowData.type === 'Revenue' &&
                  rowNode.level === 0 &&
                  (project?.project?.billing_type === billingTypes.FIXED_BID || project?.project?.billing_type === billingTypes.MONTHLY_RETAINER);
                else monthlyValues
              "
              (click)="project?.project?.billing_type === billingTypes.MONTHLY_RETAINER ? openRevenueLockDialog(rowNode, rowData, monthlyData, index) : ''"
            >
              <span
                [ngClass]="{ underline: getLockUnlockSign(monthlyData, index) === 'unlock' }"
                *hasAnyPermission="[permissionModules.VIEW_FIXED_BID_LOCK, permissionModules.MANAGE_FIXED_BID_LOCK]; hideTemplate: true; disableEvent: true"
                (click)="clickableIcon(monthlyData, index) ? openRevenueLockDialog(rowNode, rowData, monthlyData, index) : ''"
              >
                <span *hasAnyPermission="permissionModules.MANAGE_FIXED_BID_LOCK; disableEvent: true" [ngClass]="{ 'cursor-pointer': clickableIcon(monthlyData, index) }">
                  <span *ngIf="getLockUnlockSign(monthlyData, index) === 'lock' && project?.project?.billing_type === billingTypes.FIXED_BID">
                    <em class="fa-solid fa-lock"></em>
                  </span>
                  <span *ngIf="getLockUnlockSign(monthlyData, index) === 'unlock' && project?.project?.billing_type === billingTypes.FIXED_BID">
                    <em class="fa-solid fa-unlock"></em>
                  </span>
                </span>
              </span>
              ${{ monthlyData?.value | addCommasToNumbers }}
            </span>
            <ng-template #monthlyValues>
              {{ rowData.type !== 'Gross Margin' ? '$' : '' }}{{ monthlyData?.value | addCommasToNumbers }}{{ rowData.type === 'Gross Margin' ? '%' : '' }}
            </ng-template>
          </td>
          <td class="font-weight-bold">
            {{ rowData.type !== 'Gross Margin' ? '$' : '' }} {{ getTotal(rowData) | addCommasToNumbers }}{{ rowData.type === 'Gross Margin' ? '%' : '' }}
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="frozenbody" let-rowNode let-rowData="rowData">
        <tr>
          <td [ngClass]="getStyle(rowNode)">
            <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
            <span [title]="rowData?.type"> {{ rowData?.type }}</span>
          </td>
          <td>{{ rowData?.name }}</td>
        </tr>
      </ng-template>
      <ng-template pTemplate="emptymessage">
        <tr>
          <td class="text-left retrieving-msg">Retrieving Financial...</td>
        </tr>
      </ng-template>
    </p-treeTable>
  </div>
  <ng-template #resize_table>
    <p-treeTable
      [value]="financialReview"
      *isFetchingData="loading$"
      [columns]="tableHeaders"
      [scrollable]="true"
      [scrollHeight]="height"
      (onNodeExpand)="makeRowsSameHeight()"
      (onNodeCollapse)="makeRowsSameHeight()"
    >
      <ng-template pTemplate="colgroup" let-columns>
        <colgroup>
          <col style="width: 100px" />
          <col style="width: 100px" />
          <col *ngFor="let col of columns" style="width: 110px" />
          <col style="width: 110px" />
        </colgroup>
      </ng-template>
      <ng-template pTemplate="header" let-columns>
        <tr>
          <th></th>
          <th></th>
          <th *ngFor="let col of columns">
            {{ col.monthLabel }}
          </th>
          <th>Total</th>
        </tr>
      </ng-template>
      <ng-template pTemplate="body" let-rowData="rowData" let-rowNode>
        <tr>
          <td [ngClass]="getStyle(rowNode)">
            <p-treeTableToggler [rowNode]="rowNode"></p-treeTableToggler>
            <span [title]="rowData?.type"> {{ rowData?.type }}</span>
          </td>
          <td>{{ rowData?.name }}</td>
          <td class="text-right" *ngFor="let monthlyData of getMonthlyValues(rowData) | keyvalue : preserveOriginalOrder; let index">
            <span
              [ngClass]="{ underline: getLockUnlockSign(monthlyData, index) === 'unlock' }"
              *ngIf="rowData.type === 'Revenue' && rowNode.level === 0 && project?.project?.billing_type === billingTypes.FIXED_BID; else monthlyValues"
            >
              <span
                [ngClass]="{ underline: getLockUnlockSign(monthlyData, index) === 'unlock' }"
                *hasAnyPermission="[permissionModules.VIEW_FIXED_BID_LOCK, permissionModules.MANAGE_FIXED_BID_LOCK]; hideTemplate: true; disableEvent: true"
                (click)="clickableIcon(monthlyData, index) ? openRevenueLockDialog(rowNode, rowData, monthlyData, index) : ''"
              >
                <span *hasAnyPermission="permissionModules.MANAGE_FIXED_BID_LOCK; disableEvent: true" [ngClass]="{ 'cursor-pointer': clickableIcon(monthlyData, index) }">
                  <span *ngIf="getLockUnlockSign(monthlyData, index) === 'lock'">
                    <em class="fa-solid fa-lock"></em>
                  </span>
                  <span *ngIf="getLockUnlockSign(monthlyData, index) === 'unlock'">
                    <em class="fa-solid fa-unlock"></em>
                  </span>
                </span>
              </span>
              ${{ monthlyData?.value | addCommasToNumbers }}
            </span>
            <ng-template #monthlyValues>
              {{ rowData.type !== 'Gross Margin' ? '$' : '' }}{{ monthlyData?.value | addCommasToNumbers }}{{ rowData.type === 'Gross Margin' ? '%' : '' }}
            </ng-template>
          </td>
          <td class="font-weight-bold text-right">
            {{ rowData.type !== 'Gross Margin' ? '$' : '' }} {{ getTotal(rowData) | addCommasToNumbers }}{{ rowData.type === 'Gross Margin' ? '%' : '' }}
          </td>
        </tr>
      </ng-template>
    </p-treeTable>
  </ng-template>
</div>

<p-dialog [header]="lockValueObj?.title" [(visible)]="showLockDialog" [modal]="true" class="lock-dialog" [baseZIndex]="10000" [draggable]="false" [resizable]="false">
  <div *ngIf="lockValueObj" class="p-m-0 mt-2 mb-2">
    <div *isFetchingData="loading$$">
      <h6 class="form-group first text">{{ lockValueObj?.displayText }}</h6>
      <h5 class="form-group first" *ngIf="lockValueObj?.displayMonth">{{ lockValueObj?.displayMonth }}</h5>
      <ng-container *ngIf="!lockValueObj?.locked">
        <div class="form-group first" *ngIf="lockValueObj">
          <div class="p-inputgroup">
            <span class="p-inputgroup-addon">$</span>
            <p-inputNumber [min]="0" inputId="integeronly" [(ngModel)]="lockValueObj.amount" name="amt" (onInput)="valueChange('amount')"></p-inputNumber>
          </div>
          <small *ngIf="showAmtError" class="form-text text-danger"> Amount is required</small>
        </div>
        <div class="form-group first" *ngIf="lockValueObj">
          <h5>Note</h5>
          <textarea rows="5" cols="30" pInputTextarea [(ngModel)]="lockValueObj.reason" name="reason" (input)="valueChange('reason')"></textarea>
          <small *ngIf="showNoteError" class="form-text text-danger"> Note is required</small>
        </div>
      </ng-container>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <div class="d-flex flex-wrap justify-content-end align-items-center" *isFetchingData="loading$$">
      <ng-container *ngIf="lockValueObj?.locked; else unLocked">
        <button type="button" class="btn-cancel" (click)="closeLockModal()">Okay</button>
      </ng-container>
      <ng-template #unLocked>
        <button type="button" class="btn-cancel" (click)="closeLockModal()">Cancel</button>
        <button type="button" class="btn-save" (click)="removeLock()" [isSubmitting]="isRemoveSubmitting" *ngIf="lockValueObj?.isEdit">Remove</button>
        <button type="button" class="btn-save" (click)="saveLockValue()" *ngIf="project?.project?.billing_type === 'Fixed Bid'" [isSubmitting]="isSubmitting">
          {{ lockValueObj?.isEdit ? 'Update' : ' Lock Revenue' }}
        </button>
        <button type="button" class="btn-save" (click)="saveLockValue()" *ngIf="project?.project?.billing_type === billingTypes.MONTHLY_RETAINER" [isSubmitting]="isSubmitting">
          {{ lockValueObj?.isEdit ? 'Update' : ' Save' }}
        </button>
      </ng-template>
    </div>
  </ng-template>
</p-dialog>
